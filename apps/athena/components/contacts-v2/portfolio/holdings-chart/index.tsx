import React, { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, Button, DateSelect, Tooltip } from '@ds';
import {
  ChartError,
  ChartLoading,
  ChartNoData,
  ChartTooltip,
  ChartWrapper,
  Tick,
  Typography,
  UptrendSparklineIcon,
  DowntrendSparklineIcon,
  StaticSparklineIcon,
} from '@ds';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  CalendarIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from '@heroicons/react/outline';
import clsx from 'clsx';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import timezonePlugin from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { useRouter } from 'next/router';
import numeral from 'numeral';
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
} from 'recharts';
import {
  ContactQuery,
  MediaAnnouncement,
  useContactShareholdingSummaryQuery,
} from '@/apollo/generated';
import { useContactProfileContext } from '@/components/contacts-v2/contacts-context';
import OverlayPicker from '@/components/contacts-v2/portfolio/holdings-chart/overlay-picker';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import useFeatureEnabled, { FLAGS } from '@/hooks/use-feature-toggles';
import { getCurrencySymbol } from '@/utils/common-helpers';

dayjs.extend(utc);
dayjs.extend(timezonePlugin);
dayjs.extend(customParseFormat);

const volumeColor = '#D2D6DB';
const sharePriceColor = '#0BA5EC';
const netHoldingColor = '#384250';
const announcementsColor = '#6C737F';
const beneficialOwnerHoldingsColor = '#059669';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const AnnouncementDot = (props: any) => {
  const { cx, cy, payload } = props;
  if (payload?.announcements.length) {
    return <circle cx={cx} cy={cy} fill={announcementsColor} r={4} />;
  }
  return null;
};

type ChartData = Array<{
  announcements?: Array<Partial<MediaAnnouncement> | null>;
  beneficialOwnerHolding?: number | null;
  beneficialOwnerHoldingPredicted?: number | null;
  close?: number | null;
  date?: string;
  id?: string;
  netHolding?: number | null;
  volume?: number | null;
}>;

interface Props {
  contact: NonNullable<ContactQuery['contact']>;
  isOverviewTab?: boolean;
}

const ContactShareholdingChart: React.FC<Props> = ({
  contact,
  isOverviewTab = false,
}) => {
  const { latestRegisterReportDate } = useCurrentCompanyProfileUser();
  const { totalShareholdings } = useContactProfileContext();

  const router = useRouter();

  const {
    profile: { currency, timezone },
  } = useCurrentCompanyProfileUser();

  const isExtraSharePricesEnabled = useFeatureEnabled(FLAGS.extraSharePrices);

  const [showSharePrice, setShowSharePrice] = React.useState(
    isOverviewTab ? false : true
  );

  const [showVolume, setShowVolume] = React.useState(false);
  const [showAnnouncements, setShowAnnouncements] = React.useState(false);
  const [showBeneficialOwnerHoldings, setShowBeneficialOwnerHoldings] =
    React.useState(true);
  const [chartData, setChartData] = React.useState<ChartData>([]);
  const [updatingData, setUpdatingData] = React.useState(true);

  const earliestPurchaseDate = useMemo(() => {
    if (!contact?.shareholdings?.length) return null;

    const earliestDate = contact.shareholdings.reduce(
      (earliest: dayjs.Dayjs | null, shareholding) => {
        if (!shareholding.initialPurchaseDate) return earliest;
        const date = dayjs(shareholding.initialPurchaseDate);
        return !earliest || date.isBefore(earliest) ? date : earliest;
      },
      null
    );

    return dayjs(earliestDate).subtract(1, 'day')?.toDate();
  }, [contact?.shareholdings]);

  const [startDate, setStartDate] = React.useState<Maybe<Date>>(
    dayjs().add(-365, 'day').toDate()
  );
  const [endDate, setEndDate] = React.useState<Maybe<Date>>(
    dayjs().add(-1, 'days').toDate()
  );

  const { data, error, loading, networkStatus, refetch } =
    useContactShareholdingSummaryQuery({
      skip: !startDate || !endDate,
      variables: {
        contactId: contact?.id,
        endDate: dayjs(endDate).format('YYYY-MM-DD'),
        startDate: dayjs(startDate).format('YYYY-MM-DD'),
      },
    });

  const [expandHoldingsChart, setExpandHoldingsChart] = useState(true);

  const holdingTrend = useMemo(() => {
    const initialBalance =
      data?.contactShareholdingSummary?.dailyHoldings?.[0]?.balance;
    const finalBalance =
      data?.contactShareholdingSummary?.dailyHoldings?.[
        data?.contactShareholdingSummary?.dailyHoldings?.length - 1
      ]?.balance;

    if (
      typeof initialBalance === 'number' &&
      typeof finalBalance === 'number'
    ) {
      return initialBalance > finalBalance
        ? 'downwards'
        : initialBalance < finalBalance
        ? 'upwards'
        : 'static';
    }
    return null;
  }, [data]);

  const formattedDiffStartAndEndDate = useMemo(() => {
    const diffDays = dayjs(endDate).diff(startDate, 'days');
    const diffMonths = dayjs(endDate).diff(startDate, 'months');
    const diffYears = dayjs(endDate).diff(startDate, 'years');

    if (diffDays < 30) {
      return `${diffDays} day${diffDays === 1 ? '' : 's'}`;
    }

    if (diffMonths < 12) {
      return `${diffMonths} month${diffMonths === 1 ? '' : 's'}`;
    }

    return `${diffYears} year${diffYears === 1 ? '' : 's'}`;
  }, [startDate, endDate]);

  const titleAndTrend = useMemo(() => {
    if (isOverviewTab) {
      return (
        <div className="flex w-full items-center justify-between gap-2">
          <div>
            <div className="flex items-center gap-2">
              <Typography variant="text-heading-sm">
                Portfolio summary (past year)
              </Typography>
              {totalShareholdings > 0 && contact?.totalShareholdingRank && (
                <Tooltip
                  className="min-w-[170px] max-w-[120px] text-center"
                  description="Shareholder rank is based on the total number of shares held directly on your registry."
                >
                  <Badge size="sm">
                    Rank: #{contact?.totalShareholdingRank?.toLocaleString()}
                  </Badge>
                </Tooltip>
              )}
            </div>
            {totalShareholdings > 0 && (
              <Typography className="text-gray-700" variant="text-body-sm">
                Current holdings:{' '}
                {numeral(totalShareholdings).format('0,0[.][0000]')} shares
              </Typography>
            )}
          </div>

          <Button
            size="sm"
            variant="secondary-gray"
            onClick={() => {
              router.push(
                {
                  pathname: router.pathname,
                  query: { ...router.query, tab: 'portfolio' },
                },
                undefined,
                { shallow: true }
              );
            }}
          >
            View portfolio
          </Button>
        </div>
      );
    }

    if (expandHoldingsChart) {
      return <Typography variant="text-heading-sm">Holding history</Typography>;
    }

    return (
      <div className="flex items-center gap-4">
        {holdingTrend === 'upwards' && (
          <UptrendSparklineIcon className="h-12 w-12" />
        )}
        {holdingTrend === 'downwards' && (
          <DowntrendSparklineIcon className="h-12 w-12" />
        )}
        {holdingTrend === 'static' && (
          <StaticSparklineIcon className="h-12 w-12" />
        )}
        <div>
          <Typography variant="text-heading-sm">Holding history</Typography>
          <div className="flex items-center gap-1">
            {holdingTrend === 'upwards' && (
              <ArrowUpIcon className="h-4 w-4 text-green-600" />
            )}
            {holdingTrend === 'downwards' && (
              <ArrowDownIcon className="h-4 w-4 text-red-600" />
            )}

            <Typography>
              Trending{' '}
              <span
                className={clsx(
                  `font-bold`,
                  holdingTrend === 'upwards' ? 'text-green-600' : 'text-red-600'
                )}
              >
                {holdingTrend}
              </span>{' '}
              in the last {formattedDiffStartAndEndDate}
            </Typography>
          </div>
        </div>
      </div>
    );
  }, [
    contact?.totalShareholdingRank,
    expandHoldingsChart,
    formattedDiffStartAndEndDate,
    holdingTrend,
    isOverviewTab,
    router,
    totalShareholdings,
  ]);

  useEffect(() => {
    if (startDate && endDate) {
      refetch();
    }
  }, [startDate, endDate, refetch]);

  useEffect(() => {
    if (chartData.length) {
      setUpdatingData(false);
    }
  }, [chartData]);

  useEffect(() => {
    if (!loading) {
      if (data?.contactShareholdingSummary?.timeseries.length) {
        const processedData = data.contactShareholdingSummary.timeseries
          .map((timeseries) => {
            const netHolding =
              data.contactShareholdingSummary?.dailyHoldings.find(
                (sm) => sm?.date === timeseries?.date
              );

            const beneficialOwnerHolding =
              data.contactShareholdingSummary?.beneficialOwnerHoldings.find(
                (boh) => boh?.date === timeseries?.date
              );

            const announcements =
              data.contactShareholdingSummary?.announcements.filter(
                (announcement) => {
                  // Unable to make it work with tz(timezone) and isSame function here
                  return (
                    dayjs(announcement?.postedAt)
                      .tz(timezone)
                      .format('YYYY-MM-DD') ===
                    dayjs(timeseries?.date).format('YYYY-MM-DD')
                  );
                }
              );

            // Should not show daily holding for dates that have not been imported yet
            // For dates that we have imported but no daily holding, we can show 0
            return {
              ...timeseries,
              announcements,
              beneficialOwnerHolding: beneficialOwnerHolding?.balance ?? null,
              netHolding:
                netHolding?.balance ??
                (dayjs(timeseries?.date).isAfter(
                  dayjs(latestRegisterReportDate, 'DD/MM/YYYY')
                )
                  ? null
                  : 0),
            };
          })
          .filter((ts) => ts.close && ts.volume);

        const beneficialOwnerHoldings =
          data.contactShareholdingSummary?.beneficialOwnerHoldings || [];

        let latestBeneficialHolding: { balance: number; date: string } | null =
          null;
        let latestBeneficialDate: dayjs.Dayjs | null = null;

        if (beneficialOwnerHoldings.length > 0) {
          latestBeneficialHolding = beneficialOwnerHoldings
            .filter((boh) => boh?.date && boh?.balance !== null)
            .sort(
              (a, b) => dayjs(b?.date).valueOf() - dayjs(a?.date).valueOf()
            )[0];

          if (latestBeneficialHolding) {
            latestBeneficialDate = dayjs(latestBeneficialHolding.date);
          }
        }

        const chartDataWithPredicted = processedData.map((dataPoint) => {
          const dataPointDate = dayjs(dataPoint.date);
          let beneficialOwnerHoldingPredicted = null;

          if (
            latestBeneficialHolding &&
            latestBeneficialDate &&
            dataPointDate.isAfter(latestBeneficialDate, 'day') &&
            !dataPoint.beneficialOwnerHolding
          ) {
            beneficialOwnerHoldingPredicted = latestBeneficialHolding.balance;
          }

          return {
            ...dataPoint,
            beneficialOwnerHoldingPredicted,
          };
        });

        setChartData(chartDataWithPredicted);
      } else {
        setUpdatingData(false);
        setChartData([]);
      }
    }
  }, [data, latestRegisterReportDate, loading, timezone, endDate]);

  function renderChart() {
    if (loading || [1, 2, 3, 4].includes(networkStatus) || updatingData) {
      return <ChartLoading className="h-[360px] min-h-[360px]" />;
    }

    if (error) {
      return <ChartError className="h-[360px] min-h-[360px]" />;
    }

    if (!chartData.length) {
      return <ChartNoData />;
    }

    return (
      <ChartWrapper heightClasses="min-h-[360px] md:h-[360px] md:min-h-[360px]">
        <ResponsiveContainer height={370} minWidth={350} width="100%">
          <ComposedChart
            data={chartData}
            height={370}
            margin={{
              bottom: 25,
              left: 0,
              right: 20,
              top: 30,
            }}
          >
            <CartesianGrid stroke="#eee" strokeDasharray="2" vertical={false} />
            <XAxis
              dataKey="date"
              minTickGap={20}
              scale="band"
              stroke="#f5f5f5"
              tick={
                <Tick
                  // hideFirst
                  hideLast
                  dy={'12px'}
                  fontSize="14"
                  fontWeight="400"
                />
              }
              tickFormatter={(v) => dayjs(v).format('D MMM YY')}
              tickLine={false}
            />
            {showVolume ? (
              <YAxis
                hide
                orientation="right"
                stroke="#f5f5f5"
                tick={<Tick />}
                tickLine={false}
                yAxisId="volume"
              />
            ) : null}
            <YAxis
              stroke="#f5f5f5"
              tick={<Tick />}
              tickFormatter={(v) => numeral(v).format('0[.]0a')}
              tickLine={false}
            />
            {showSharePrice ? (
              <YAxis
                orientation="right"
                stroke="#f5f5f5"
                tick={<Tick />}
                tickFormatter={(v) =>
                  numeral(v).format(`${getCurrencySymbol(currency)}0,0[.]00[0]`)
                }
                tickLine={false}
                yAxisId="sharePrice"
              />
            ) : null}
            <RechartsTooltip
              content={
                <ChartTooltip
                  primaryDataKey="close"
                  showAnnouncements={showAnnouncements}
                  showExtraSharePrices={isExtraSharePricesEnabled}
                  valueFormatter="0,0[.][0000]"
                />
              }
            />
            {!isOverviewTab && (
              <Legend
                formatter={(value: string) => {
                  return (
                    <Typography
                      className="inline text-gray-700"
                      variant="text-label-sm"
                    >
                      {value}
                    </Typography>
                  );
                }}
                iconSize={12}
                verticalAlign="bottom"
              />
            )}
            {showVolume ? (
              <Bar
                barSize={20}
                dataKey="volume"
                fill={volumeColor}
                isAnimationActive={false}
                legendType="square"
                name="Volume"
                yAxisId="volume"
              />
            ) : null}
            {totalShareholdings > 0 ? (
              <Line
                connectNulls
                dataKey="netHolding"
                dot={showAnnouncements ? <AnnouncementDot /> : false}
                isAnimationActive={false}
                legendType="plainline"
                name="Holdings"
                stroke={netHoldingColor}
                strokeWidth={2}
                type="monotone"
              />
            ) : null}
            {showBeneficialOwnerHoldings ? (
              <Line
                connectNulls
                dataKey="beneficialOwnerHolding"
                dot={
                  data?.contactShareholdingSummary?.beneficialOwnerHoldings
                    .length == 1
                    ? { fill: beneficialOwnerHoldingsColor, r: 2 }
                    : false
                }
                isAnimationActive={false}
                legendType="plainline"
                name="Unmasked holdings"
                stroke={beneficialOwnerHoldingsColor}
                strokeWidth={2}
                type="monotone"
              />
            ) : null}

            {showBeneficialOwnerHoldings ? (
              <Line
                connectNulls
                dataKey="beneficialOwnerHoldingPredicted"
                dot={false}
                isAnimationActive={false}
                legendType="none"
                name="Unmasked holdings (Implied)"
                stroke={beneficialOwnerHoldingsColor}
                strokeDasharray="5 5"
                strokeWidth={2}
                type="monotone"
              />
            ) : null}

            {showSharePrice ? (
              <Line
                connectNulls
                dataKey="close"
                dot={false}
                isAnimationActive={false}
                legendType="plainline"
                name="Share price"
                stroke={sharePriceColor}
                strokeWidth={2}
                type="monotone"
                yAxisId="sharePrice"
              />
            ) : null}
            {showAnnouncements ? (
              <Line
                dataKey="announcements"
                dot={false}
                isAnimationActive={false}
                legendType="circle"
                name="Announcements"
                stroke={announcementsColor}
                strokeWidth={0}
                type="monotone"
              />
            ) : null}
          </ComposedChart>
        </ResponsiveContainer>
      </ChartWrapper>
    );
  }
  return (
    <div
      className={`rounded-lg border border-gray-200 bg-white ${
        !expandHoldingsChart && 'cursor-pointer'
      }`}
      onClick={() => !expandHoldingsChart && setExpandHoldingsChart(true)}
    >
      <div
        className={`ease-in-outmd:items-center flex flex-row justify-between gap-4 px-6 py-2 transition-[border] duration-300 ${
          expandHoldingsChart
            ? 'border-b border-gray-200'
            : 'border-b-0 border-transparent'
        }`}
      >
        {titleAndTrend}

        {!isOverviewTab && (
          <div className="relative flex items-center gap-2">
            <div className={expandHoldingsChart ? '' : 'hidden'}>
              <OverlayPicker
                displayBeneficialOwnerHoldingsItem={
                  contact?.beneficialOwnerAccounts?.length > 0
                }
                setShowAnnouncements={setShowAnnouncements}
                setShowBeneficialOwnerHoldings={setShowBeneficialOwnerHoldings}
                setShowSharePrice={setShowSharePrice}
                setShowVolume={setShowVolume}
                showAnnouncements={showAnnouncements}
                showBeneficialOwnerHoldings={showBeneficialOwnerHoldings}
                showSharePrice={showSharePrice}
                showVolume={showVolume}
              />
            </div>
            <div className={expandHoldingsChart ? '' : 'hidden'}>
              <DateSelect
                customDates
                enableAllTimeOption
                fullWidth
                LeadingIcon={<CalendarIcon className="h-4 w-4" />}
                endDate={endDate}
                label={'no-label'}
                maxDate={new Date()}
                minDate={
                  earliestPurchaseDate !== null
                    ? earliestPurchaseDate
                    : dayjs('1901-01-01').toDate()
                }
                pastXDays={365}
                setEndDate={setEndDate}
                setStartDate={setStartDate}
                startDate={startDate}
              />
            </div>

            <div
              className="ml-2 cursor-pointer transition-transform duration-300"
              onClick={() => setExpandHoldingsChart(!expandHoldingsChart)}
            >
              {expandHoldingsChart ? (
                <ChevronUpIcon className="h-4 w-4" />
              ) : (
                <ChevronDownIcon className="h-4 w-4" />
              )}
            </div>
          </div>
        )}
      </div>

      <div
        className={`overflow-hidden transition-[max-height] duration-300 ease-in-out ${
          expandHoldingsChart ? 'max-h-[500px]' : 'max-h-0'
        }`}
      >
        {renderChart()}
      </div>
    </div>
  );
};

export default ContactShareholdingChart;
