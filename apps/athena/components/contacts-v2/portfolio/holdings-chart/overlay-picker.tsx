import { useEffect, useMemo, useState } from 'react';
import { dropdownStyles, Typography } from '@ds';
import { Listbox, Transition } from '@headlessui/react';
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from '@heroicons/react/outline';
import { Square3Stack3DIcon } from '@heroicons/react-v2/24/outline';
import clsx from 'clsx';

const OverlayPicker: React.ComponentType<{
  displayBeneficialOwnerHoldingsItem: boolean;
  setShowAnnouncements: React.Dispatch<React.SetStateAction<boolean>>;
  setShowBeneficialOwnerHoldings: React.Dispatch<React.SetStateAction<boolean>>;
  setShowSharePrice: React.Dispatch<React.SetStateAction<boolean>>;
  setShowVolume: React.Dispatch<React.SetStateAction<boolean>>;
  showAnnouncements: boolean;
  showBeneficialOwnerHoldings: boolean;
  showSharePrice: boolean;
  showVolume: boolean;
}> = ({
  displayBeneficialOwnerHoldingsItem,
  setShowAnnouncements,
  setShowBeneficialOwnerHoldings,
  setShowSharePrice,
  setShowVolume,
  showAnnouncements,
  showBeneficialOwnerHoldings,
  showSharePrice,
  showVolume,
}) => {
  const [noOverlay, setNoOverlay] = useState(false);

  useEffect(() => {
    if (
      showSharePrice ||
      showVolume ||
      showAnnouncements ||
      showBeneficialOwnerHoldings
    ) {
      setNoOverlay(false);
    }
    if (
      !showSharePrice &&
      !showVolume &&
      !showAnnouncements &&
      !showBeneficialOwnerHoldings
    ) {
      setNoOverlay(true);
    }
  }, [
    noOverlay,
    setShowAnnouncements,
    setShowBeneficialOwnerHoldings,
    setShowSharePrice,
    setShowVolume,
    showAnnouncements,
    showBeneficialOwnerHoldings,
    showSharePrice,
    showVolume,
  ]);

  useEffect(() => {
    if (noOverlay) {
      setShowSharePrice(false);
      setShowVolume(false);
      setShowAnnouncements(false);
      setShowBeneficialOwnerHoldings(false);
    }
  }, [
    noOverlay,
    setShowAnnouncements,
    setShowBeneficialOwnerHoldings,
    setShowSharePrice,
    setShowVolume,
  ]);

  const options = useMemo(() => {
    const baseOptions = [
      {
        label: 'No overlay',
        setValue: setNoOverlay,
        value: noOverlay,
      },
      {
        label: 'Share price',
        setValue: setShowSharePrice,
        value: showSharePrice,
      },
      {
        label: 'Volume',
        setValue: setShowVolume,
        value: showVolume,
      },
      {
        label: 'Market-sensitive announcements',
        setValue: setShowAnnouncements,
        value: showAnnouncements,
      },
    ];

    const beneficialOwnerOption = displayBeneficialOwnerHoldingsItem
      ? [
          {
            label: 'Unmasked holdings',
            setValue: setShowBeneficialOwnerHoldings,
            value: showBeneficialOwnerHoldings,
          },
        ]
      : [];

    return baseOptions.concat(beneficialOwnerOption);
  }, [
    noOverlay,
    setShowAnnouncements,
    setShowBeneficialOwnerHoldings,
    setShowSharePrice,
    setShowVolume,
    showAnnouncements,
    showBeneficialOwnerHoldings,
    showSharePrice,
    showVolume,
    displayBeneficialOwnerHoldingsItem,
  ]);

  const title = useMemo(() => {
    const overlayCount = options.filter((option) => option.value).length;
    const title = noOverlay
      ? 'Overlay'
      : overlayCount > 1
      ? `Overlays (${overlayCount})`
      : options.find((option) => option.value)?.label;

    return (
      <div className="flex items-center gap-1">
        <Square3Stack3DIcon className="h-4 w-4" />
        <Typography variant="text-body-sm">{title}</Typography>
      </div>
    );
  }, [noOverlay, options]);

  return (
    <div className="w-full lg:w-auto">
      <Listbox>
        {({ open }) => (
          <>
            <Listbox.Button className="relative flex w-full cursor-default items-center justify-between gap-2 rounded-lg border border-secondary-grey-light bg-white px-3 py-2 text-left text-base font-semibold text-text-main focus:border-secondary-grey-dark focus:outline-none">
              {title}
              {open ? (
                <ChevronUpIcon className="h-4 w-4" />
              ) : (
                <ChevronDownIcon className="h-4 w-4" />
              )}
            </Listbox.Button>
            <Transition>
              <Listbox.Options className="absolute z-10 mt-1 max-h-60 max-w-[300px] cursor-pointer overflow-auto rounded-md bg-white py-1 text-base font-normal shadow-lg ring-1 ring-black/5 focus:outline-none md:left-0">
                {options.map((option) => (
                  <Listbox.Option
                    key={option.label}
                    className={clsx(
                      'text-gray-700',
                      dropdownStyles['dropdown-item'],
                      option.value === true
                        ? dropdownStyles['dropdown-item-selected']
                        : dropdownStyles['dropdown-item-unselected']
                    )}
                    value={option.value}
                    onClick={() =>
                      option.setValue && option.setValue(!option.value)
                    }
                  >
                    <Typography variant="text-body-sm">
                      {option.label}
                    </Typography>
                    {option.value === true && (
                      <CheckIcon className="h-5 w-5 text-gray-900 " />
                    )}
                  </Listbox.Option>
                ))}
              </Listbox.Options>
            </Transition>
          </>
        )}
      </Listbox>
    </div>
  );
};

export default OverlayPicker;
