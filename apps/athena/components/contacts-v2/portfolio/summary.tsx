import { useMemo } from 'react';
import { <PERSON><PERSON>, Tooltip, Typography } from '@ds';
import {
  CheckCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  MinusCircleIcon,
  PlusCircleIcon,
  ScaleIcon,
} from '@heroicons/react/outline';
import { ArrowTopRightOnSquareIcon } from '@heroicons/react-v2/24/outline';
import clsx from 'clsx';
import dayjs from 'dayjs';
import Link from 'next/link';
import {
  Contact,
  useLatestCompletedImportingAndProcessingBeneficialOwnersReportsQuery,
} from '@/apollo/generated';
import { useContactProfileContext } from '@/components/contacts-v2/contacts-context';
import { renderPortfolioSummaryType } from '@/components/contacts-v2/layout/profile-layout-right-sidebar';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import { formatMoney } from '@/utils/common-helpers';
import { holdingInYearsAndMonths } from '@/utils/registry-helper';
import routes from '@/utils/routes';

interface Props {
  contact: Contact;
  currency: string;
}

const ContactPortfolioSummary: React.ComponentType<Props> = ({
  contact,
  currency,
}) => {
  const {
    currentCompanyProfileUser: {
      profile: {
        ticker: { marketListingKey },
      },
    },
    isUK,
    price,
  } = useCurrentCompanyProfileUser();

  const { data, loading } =
    useLatestCompletedImportingAndProcessingBeneficialOwnersReportsQuery();

  const hasCompletedReport = useMemo(() => {
    return !!data?.latestCompletedImportingAndProcessingBeneficialOwnersReports
      ?.latestCompletedReport;
  }, [data]);

  const {
    totalBeneficialOwnerAccountShares,
    totalBeneficialOwnerAccounts,
    totalEstimatedPurchaseValue,
    totalEstimatedSaleValue,
    totalProfitLoss,
    totalShareholdings,
  } = useContactProfileContext();

  const totalShareholdingsValue = useMemo(() => {
    if (!price) return;
    return `(${formatMoney(totalShareholdings * price, currency)})`;
  }, [totalShareholdings, price, currency]);

  const totalBeneficalOwnerSharesValue = useMemo(() => {
    if (!price) return;
    return `(${formatMoney(
      totalBeneficialOwnerAccountShares * price,
      currency
    )})`;
  }, [totalBeneficialOwnerAccountShares, price, currency]);

  const minCurrentHoldingStartDate = useMemo(() => {
    if (!contact?.shareholdings) return;
    const minCurrentHoldingStartDate = contact.shareholdings.reduce<
      string | null
    >((acc, { currentHoldingStartDate }) => {
      if (acc && currentHoldingStartDate) {
        return dayjs(acc).isBefore(currentHoldingStartDate)
          ? acc
          : currentHoldingStartDate;
      }

      return currentHoldingStartDate ? currentHoldingStartDate : acc;
    }, null);

    return minCurrentHoldingStartDate;
  }, [contact?.shareholdings]);

  const timeHeld = useMemo(() => {
    if (!minCurrentHoldingStartDate) return '-';
    const timeHeld = holdingInYearsAndMonths(minCurrentHoldingStartDate, true);
    return timeHeld;
  }, [minCurrentHoldingStartDate]);

  const portfolioSummaryTypes = useMemo(() => {
    const types = [];

    if (contact && contact?.totalShareholdingRank) {
      if (!contact || contact?.totalShareholdingRank == 0) {
        // do nothing
      } else if (contact?.totalShareholdingRank <= 20) {
        types.push('Top 20');
      } else if (contact?.totalShareholdingRank <= 50) {
        types.push('Top 50');
      } else if (contact?.totalShareholdingRank <= 100) {
        types.push('Top 100');
      }
    }

    return types;
  }, [contact]);

  const renderBOHoldings = useMemo(() => {
    const title = isUK ? 'Retail holdings' : 'Unmasked holdings';

    return (
      <Card
        LeadingIcon={<ScaleIcon className="h-4 w-4" />}
        content={
          loading || hasCompletedReport ? (
            <div className="flex items-center gap-1">
              {totalBeneficialOwnerAccounts > 0 && (
                <Tooltip
                  description={`Last updated ${dayjs(
                    data
                      ?.latestCompletedImportingAndProcessingBeneficialOwnersReports
                      ?.latestCompletedReport?.reportDate
                  ).format('MMMM D, YYYY')}`}
                  place="right"
                >
                  <div className="relative h-3.5 w-3.5 overflow-hidden">
                    <div className="absolute left-[3px] top-[3px] h-2 w-2 rounded-full bg-amber-500" />
                    <div className="absolute left-0 top-0 h-3.5 w-3.5 rounded-full bg-amber-500 opacity-50" />
                  </div>
                </Tooltip>
              )}
              <Typography className="text-gray-700" variant="text-label-md">
                {totalBeneficialOwnerAccounts > 0
                  ? totalBeneficialOwnerAccountShares.toLocaleString()
                  : '-'}
              </Typography>
              <Typography className="text-gray-700" variant="text-body-sm">
                {totalBeneficalOwnerSharesValue}
              </Typography>
            </div>
          ) : (
            <div className="flex items-center gap-1">
              <Tooltip description="Learn more" place="right">
                <Link
                  href={routes.investors.beneficialOwners.href(
                    marketListingKey as string
                  )}
                  target="_blank"
                >
                  <div className="flex items-center gap-1 text-gray-500">
                    <Badge
                      TrailingIcon={ArrowTopRightOnSquareIcon}
                      color="sunflower"
                      size="sm"
                    >
                      Coming soon
                    </Badge>
                  </div>
                </Link>
              </Tooltip>
            </div>
          )
        }
        title={title}
        tooltipDescription="Shares held under nominee accounts on your registry"
      />
    );
  }, [
    isUK,
    data?.latestCompletedImportingAndProcessingBeneficialOwnersReports
      ?.latestCompletedReport?.reportDate,
    totalBeneficialOwnerAccounts,
    totalBeneficalOwnerSharesValue,
    totalBeneficialOwnerAccountShares,
    loading,
    hasCompletedReport,
    marketListingKey,
  ]);
  const profit = useMemo(() => {
    const totalProfit =
      typeof price === 'number'
        ? totalShareholdings * price +
          totalEstimatedSaleValue -
          totalEstimatedPurchaseValue
        : totalProfitLoss;

    const percent = totalProfit / totalEstimatedPurchaseValue;

    return { percent, total: totalProfit };
  }, [
    price,
    totalProfitLoss,
    totalEstimatedPurchaseValue,
    totalEstimatedSaleValue,
    totalShareholdings,
  ]);

  if (
    (!contact.shareholdings || contact.shareholdings.length === 0) &&
    (!contact.beneficialOwnerAccounts ||
      contact.beneficialOwnerAccounts.length === 0)
  )
    return null;

  const renderProfitLoss = () => {
    if (profit.total > 0) {
      return (
        <div className="flex items-center gap-1 text-green-600">
          <Typography variant="text-label-md">
            {formatMoney(profit.total, currency)}
          </Typography>
          {profit.percent &&
            !isNaN(profit.percent) &&
            isFinite(profit.percent) && (
              <Badge>
                <div className="flex items-center gap-1">
                  {profit?.percent?.toFixed(2)}%
                </div>
              </Badge>
            )}
        </div>
      );
    } else if (profit.percent < 0) {
      return (
        <div className="flex items-center gap-1 text-red-600">
          <Typography variant="text-label-md">
            {formatMoney(profit.total, currency)}
          </Typography>
          {profit.percent &&
            !isNaN(profit.percent) &&
            isFinite(profit.percent) && (
              <Badge>
                <div className="flex items-center gap-1">
                  {profit?.percent?.toFixed(2)}%
                </div>
              </Badge>
            )}
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-1 text-gray-600">
          <Typography variant="text-label-md">
            {formatMoney(profit.total, currency)}
          </Typography>
          <Badge>0%</Badge>
        </div>
      );
    }
  };

  const renderTitleAndRank = () => {
    return (
      <div className="flex items-center gap-2 px-6 py-2">
        <Typography variant="text-heading-sm">Portfolio Summary</Typography>
        {contact.shareholdings.length > 0 && contact?.totalShareholdingRank && (
          <Tooltip
            className="min-w-[170px] max-w-[120px] text-center"
            description="Shareholder rank is based on the total number of shares held directly on your registry."
          >
            <Badge>
              Rank #{contact?.totalShareholdingRank.toLocaleString()}
            </Badge>
          </Tooltip>
        )}
      </div>
    );
  };

  // UK RENDER
  if (isUK) {
    return (
      <div className="space-y-4 rounded-lg border bg-white">
        <div>
          {renderTitleAndRank()}

          <Divider />

          {/* FIRST ROW */}
          <div className="grid sm:grid-cols-3">
            <div className="border-b sm:border-b-0 sm:border-r">
              <Card
                LeadingIcon={<ScaleIcon className="h-4 w-4" />}
                content={
                  <div className="flex items-center gap-1">
                    <Typography
                      className="text-gray-700"
                      variant="text-label-md"
                    >
                      {totalShareholdings.toLocaleString()}
                    </Typography>
                    <Typography
                      className="text-gray-700"
                      variant="text-body-sm"
                    >
                      {totalShareholdingsValue}
                    </Typography>
                  </div>
                }
                title="Register holdings"
                tooltipDescription="Shares held directly on your registry, across all linked accounts."
              />
            </div>
            <div className="border-b sm:border-b-0 sm:border-r">
              {renderBOHoldings}
            </div>

            <Card
              LeadingIcon={<CurrencyDollarIcon className="h-4 w-4" />}
              content={
                <div className="flex items-center gap-2">
                  {renderProfitLoss()}
                </div>
              }
              title="Market value change"
              tooltipDescription="Estimated unrealised profit at last closing price."
            />
          </div>

          <Divider />

          {/* SECOND ROW */}
          {contact.shareholdings.length > 0 && (
            <div className="grid sm:grid-cols-2">
              <div className="border-b sm:border-r">
                <Card
                  LeadingIcon={<ClockIcon className="h-4 w-4" />}
                  content={
                    <div className="flex items-center gap-1">
                      <Typography
                        className="text-gray-700"
                        variant="text-label-md"
                      >
                        {timeHeld}
                      </Typography>
                      <Typography
                        className="text-gray-700"
                        variant="text-body-sm"
                      >
                        (since{' '}
                        {dayjs(minCurrentHoldingStartDate).format(
                          'DD MMM YYYY'
                        )}
                        )
                      </Typography>
                    </div>
                  }
                  title="Current holding period"
                  tooltipDescription="Time held of current position. Resets when shareholder churns."
                />
              </div>
              <div>
                <Card
                  LeadingIcon={<CheckCircleIcon className="h-4 w-4" />}
                  content={
                    <div className="flex items-center gap-2">
                      {portfolioSummaryTypes.length > 0
                        ? portfolioSummaryTypes.map((type: string) => {
                            return renderPortfolioSummaryType(type);
                          })
                        : '-'}
                    </div>
                  }
                  title="Shareholder type"
                  tooltipDescription="Automatically detected shareholder traits."
                />
              </div>
            </div>
          )}

          <Divider />
        </div>
      </div>
    );
  }

  // AU RENDER
  return (
    <div className="space-y-4 rounded-lg border bg-white">
      <div>
        {renderTitleAndRank()}

        <Divider />

        {/* FIRST ROW */}
        <div className="grid sm:grid-cols-2">
          <div className="border-b sm:border-b-0 sm:border-r">
            <Card
              LeadingIcon={<ScaleIcon className="h-4 w-4" />}
              content={
                <div className="flex items-center gap-1">
                  <Typography className="text-gray-700" variant="text-label-md">
                    {totalShareholdings.toLocaleString()}
                  </Typography>
                  <Typography className="text-gray-700" variant="text-body-sm">
                    {totalShareholdingsValue}
                  </Typography>
                </div>
              }
              title="Register holdings"
              tooltipDescription="Shares held directly on your registry, across all linked accounts."
            />
          </div>

          {renderBOHoldings}
        </div>

        {/* SECOND ROW */}
        {contact.shareholdings.length > 0 && (
          <>
            <Divider />

            <div className="grid sm:grid-cols-2">
              <div className="border-b sm:border-b-0 sm:border-r">
                <Card
                  LeadingIcon={<ClockIcon className="h-4 w-4" />}
                  content={
                    <div className="flex items-center gap-1">
                      <Typography
                        className="text-gray-700"
                        variant="text-label-md"
                      >
                        {timeHeld}
                      </Typography>
                      {minCurrentHoldingStartDate && (
                        <Typography
                          className="text-gray-700"
                          variant="text-body-sm"
                        >
                          (since{' '}
                          {dayjs(minCurrentHoldingStartDate).format(
                            'DD MMM YYYY'
                          )}
                          )
                        </Typography>
                      )}
                    </div>
                  }
                  title="Time held"
                  tooltipDescription="Time held of current position. Resets when shareholder churns."
                />
              </div>
              <div>
                <Card
                  LeadingIcon={<CheckCircleIcon className="h-4 w-4" />}
                  content={
                    <div className="flex items-center gap-2">
                      {portfolioSummaryTypes.length > 0
                        ? portfolioSummaryTypes.map((type: string) => {
                            return renderPortfolioSummaryType(type);
                          })
                        : '-'}
                    </div>
                  }
                  title="Shareholder type"
                  tooltipDescription="Automatically detected shareholder traits."
                />
              </div>
            </div>
          </>
        )}

        {/* THIRD ROW */}
        {contact.shareholdings.length > 0 && (
          <>
            <Divider />
            <div className="grid sm:grid-cols-3">
              <div className="border-b sm:border-b-0 sm:border-r">
                <Card
                  LeadingIcon={<CurrencyDollarIcon className="h-4 w-4" />}
                  content={
                    <div className="flex items-center gap-2">
                      {renderProfitLoss()}
                    </div>
                  }
                  title={profit.total > 0 ? 'In profit' : 'In loss'}
                  tooltipDescription="Estimated unrealised profit at last closing price."
                />
              </div>
              <div className="border-b sm:border-b-0 sm:border-r">
                <Card
                  LeadingIcon={<PlusCircleIcon className="h-4 w-4" />}
                  content={
                    <div className="flex items-center gap-2">
                      {/* <Typography className="text-gray-700" variant="text-label-md">
                    {Math.round(totalShareholdings).toLocaleString()}
                  </Typography> */}
                      <Typography
                        className="text-gray-700"
                        variant="text-body-sm"
                      >
                        {formatMoney(totalEstimatedPurchaseValue, currency)}
                      </Typography>
                    </div>
                  }
                  title="Total bought"
                  tooltipDescription="Total shares bought. Value is calculated using estimated price of transaction."
                />
              </div>
              <div>
                <Card
                  LeadingIcon={<MinusCircleIcon className="h-4 w-4" />}
                  content={
                    <div className="flex items-center gap-2">
                      {/* <Typography className="text-gray-700" variant="text-label-md">
                    {Math.round(totalShareholdings).toLocaleString()}
                  </Typography> */}

                      <Typography
                        className="text-gray-700"
                        variant="text-body-sm"
                      >
                        {formatMoney(totalEstimatedSaleValue, currency)}
                      </Typography>
                    </div>
                  }
                  title="Total sold"
                  tooltipDescription="Total shares sold.  Value is calculated using estimated price of transaction."
                />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

interface CardProps {
  LeadingIcon: React.ReactNode;
  content: React.ReactNode;
  title: string;
  tooltipDescription: string;
}

const Card: React.ComponentType<CardProps> = ({
  LeadingIcon,
  content,
  title,
  tooltipDescription,
}) => (
  <div className="rounded-lg bg-white p-4">
    <div className="flex items-start gap-2">
      <span className="flex h-10 w-10 items-center justify-center rounded-xl bg-green-50 text-green-600">
        {LeadingIcon}
      </span>

      <div>
        <div className="flex items-center gap-2">
          <Typography className="text-gray-500" variant="text-body-sm">
            {title}
          </Typography>
          <Tooltip key={tooltipDescription} description={tooltipDescription} />
        </div>
        {content}
      </div>
    </div>
  </div>
);

const Divider: React.ComponentType<{ className?: string }> = ({
  className,
}) => (
  <div
    className={clsx(className, 'flex items-center justify-between border-b')}
  />
);

export default ContactPortfolioSummary;
