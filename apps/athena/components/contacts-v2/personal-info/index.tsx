import { createElement, ReactNode, use<PERSON>emo, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typo<PERSON> } from '@ds';

import {
  IdentificationIcon,
  QuestionMarkCircleIcon,
  ChatBubbleOvalLeftEllipsisIcon,
  CalendarIcon,
  TagIcon,
  BellIcon,
  FunnelIcon,
  BriefcaseIcon,
  BuildingOfficeIcon,
  PhoneIcon,
  HashtagIcon,
  HomeIcon,
  InformationCircleIcon,
  CurrencyDollarIcon,
  NoSymbolIcon,
  UserCircleIcon,
} from '@heroicons/react-v2/24/outline';
import { StarIcon } from '@heroicons/react-v2/24/solid';
import dayjs from 'dayjs';
import fp from 'lodash/fp';

import { HeroiconV2Type } from '@leaf/helpers';
import {
  ContactQuery,
  Shareholding,
  useUpdateContactMutation,
} from '@/apollo/generated';
import { useContactActivityLogContext } from '@/components/contacts/profile/activity-log/activity-log-context';
import Card from '@/components/contacts-v2/common/card';
import { <PERSON><PERSON>, Column } from '@/components/contacts-v2/common/grid';
import EditableTags from '@/components/contacts-v2/common/tags';
import EditableAddressField from '@/components/contacts-v2/personal-info/editable-address-field';
import EditableChipField from '@/components/contacts-v2/personal-info/editable-chip-field';
import EditableText from '@/components/contacts-v2/personal-info/editable-field';
import EditableFieldList from '@/components/contacts-v2/personal-info/editable-list-field';
import { useContactMemento } from '@/components/contacts-v2/personal-info/hooks/use-contact-memento';
import LeadStatus from '@/components/contacts-v2/personal-info/lead';
import QualifiedInvestor from '@/components/contacts-v2/personal-info/qualified-investor';
import Subscriptions from '@/components/contacts-v2/personal-info/subscription';
import { useAlert } from '@/contexts/alert-context';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import { singleLineAddress } from '@/utils/contacts-helper';
import { formatError } from '@/utils/error-helpers';

const SOURCE_MAP = {
  BENEFICIAL_OWNERS_IMPORT: 'From nominee unmasking',
  BULK_IMPORT: 'Uploaded from list',
  HUB_SIGNUP: 'Signed up to your hub',
  MANUAL_CREATION: 'Manually created',
  OTHER: 'Other',
  REGISTRY_IMPORT: 'Imported from registry',
  SUBSCRIBE_FORM: 'Subscribed to email list',
};

interface Props {
  contact: NonNullable<ContactQuery['contact']>;
  contactLoading: boolean;
}

const STAR_ICON = {
  icon: (props: { className?: string }) => (
    <StarIcon {...props} className="text-sunflower-400 h-4 w-4" />
  ),
  position: 'end' as 'end' | 'start',
};

const NO_SYMBOL_ICON = {
  icon: (props: { className?: string }) => (
    <Tooltip content="This contact can't receive emails. They could have marked an email as spam or had multiple emails bounce (fail to deliver).">
      <NoSymbolIcon {...props} className="h-4 w-4" />
    </Tooltip>
  ),
  position: 'start' as 'end' | 'start',
};

const AVAILABLE_ITEMS = [
  'Under 18',
  '19-24',
  '25-34',
  '35-44',
  '45-54',
  '55-64',
  '65+',
];

const REGISTRY_LOGO = {
  automic: 'automic.png',
  boardroom: 'boardroom.svg',
  computershare: 'computershare.svg',
  link: 'link.svg',
  neville: 'neville.svg',
  xcend: 'xcend.svg',
};

const UNMASKED_ACCOUNT_ICON = {
  unmasked: 'unmasked.svg'
}

const groupBy = (
  list: Array<{ label: string; type?: string }>,
  registry = ''
) => {
  const typeIconMapping: Record<
    string,
    { icon: HeroiconV2Type | string; tooltip: string }
  > = {
    hub: {
      icon: '/logos/investorhub-logo.svg',
      tooltip: 'Provided by contact via hub and can not be edited or deleted',
    },
    registry: {
      icon: `/logos/${
        REGISTRY_LOGO[registry.toLowerCase() as keyof typeof REGISTRY_LOGO]
      }`,
      tooltip: 'Provided via registry and can not be edited or deleted',
    },
  };

  const processList = fp.flow(
    fp.groupBy('label'),
    fp.mapValues((itemsInGroup) => {
      const firstItem = fp.first(itemsInGroup);
      const types = fp.flow(fp.map('type'), fp.compact)(itemsInGroup);
      const count = itemsInGroup.length;
      const icons = fp
        .uniq(types)
        .map((t) => typeIconMapping[t])
        .filter(Boolean);

      return firstItem
        ? { ...firstItem, count, icons, types }
        : { count, icons, types };
    }),
    fp.values
  );

  return processList(list);
};

const Item = ({
  badgeDescription,
  icon,
  label,
  tooltipDescription,
  value,
}: {
  badgeDescription?: string | null;
  icon?: HeroiconV2Type;
  label: ReactNode;
  tooltipDescription?: string;
  value: ReactNode;
}) => {
  return (
    <Grid alignY="center">
      <Column alignY="top" span={1}>
        <div className="flex items-center py-2">
          <div className="mr-2 flex h-8 w-8 shrink-0 items-center justify-center rounded-xl bg-green-50">
            {icon &&
              createElement(icon, { className: 'h-4 w-4 text-green-500' })}
          </div>
          <Typography variant="text-body-sm">{label}</Typography>
          {tooltipDescription && (
            <Tooltip
              className="max-w-[180px]"
              description={tooltipDescription}
              place="top"
            >
              <InformationCircleIcon className="ml-1 h-4 w-4 text-gray-600" />
            </Tooltip>
          )}
          {badgeDescription && (
            <Badge className="ml-2">{badgeDescription}</Badge>
          )}
        </div>
      </Column>
      <Column span={2}>
        <div className="py-1 text-left">{value}</div>
      </Column>
    </Grid>
  );
};

const getCreatorName = (contact: NonNullable<ContactQuery['contact']>) => {
  const { creatorName, creatorUser } = contact;
  if (creatorName) return creatorName;
  if (creatorUser) return `${creatorUser.firstName} ${creatorUser.lastName}`;
  return '';
};

const createFields = (
  data: Partial<ContactQuery['contact']>,
  loading = false,
  error: string | null = null
) => {
  const keys = Object.keys(data || {});

  return keys.reduce((acc, cur) => {
    acc[cur] = { error, loading };
    return acc;
  }, {} as Record<string, { error: null | string; loading: boolean }>);
};

const contactAddress = (
  contact: NonNullable<ContactQuery['contact']> | NonNullable<Shareholding>
) => {
  return {
    components: {
      addressCity: contact?.addressCity || '',
      addressCountry: contact?.addressCountry || '',
      addressLineOne: contact?.addressLineOne || '',
      addressLineTwo: contact?.addressLineTwo || '',
      addressPostcode: contact?.addressPostcode || '',
      addressState: contact?.addressState || '',
    },
    formattedAddress: singleLineAddress(contact),
  };
};

const PersonalInfo = ({ contact, contactLoading }: Props) => {
  const { formatAndShowError, showMultiAlert } = useAlert();
  const [fields, setFields] = useState<
    Record<string, { error: null | string; loading: boolean }>
  >({});

  const [address, setAddress] = useState<AmplifyAddress>({
    ...contactAddress(contact),
  });

  const { renderedContactLatestActivity } = useContactActivityLogContext();

  const [updateContactMutation] = useUpdateContactMutation({
    awaitRefetchQueries: true,
    refetchQueries: ['Contact'],
  });

  const { currentCompanyProfileUser } = useCurrentCompanyProfileUser();

  const renderContactSource = useMemo(() => {
    if (!contact) return '';
    const { contactSource, insertedAt } = contact;
    const source = SOURCE_MAP[contactSource as keyof typeof SOURCE_MAP];

    let createdAt = '';
    if (source !== 'Other') {
      createdAt = dayjs(insertedAt).format('D MMM YYYY');
    }

    let createdBy = '';
    if (contactSource == 'MANUAL_CREATION') {
      createdBy = getCreatorName(contact);
    }

    return (
      <div className="block max-w-fit overflow-hidden text-ellipsis pl-3">
        <Tooltip
          className="max-w-[200px]"
          content={`How and when this contact was first identified on hub: \n ${source} ${
            createdBy ? `by ${createdBy}` : ''
          } ${source !== 'Other' ? `· ${createdAt}` : ''}`}
          place="top"
        >
          <Badge size="xs">
            {source} {source !== 'Other' ? `· ${createdAt}` : ''}
          </Badge>
        </Tooltip>
      </div>
    );
  }, [contact]);

  const runMutation = async (data: Partial<ContactQuery['contact']>) => {
    await updateContactMutation({
      variables: {
        contact: {
          ...data,
        },
        id: contact.id,
      },
    });
  };

  const changeData = async (
    data: Partial<ContactQuery['contact']>,
    addToHistory = true
  ) => {
    setFields(createFields(data, true));

    await runMutation(data)
      .then(() => {
        const fields = createFields(data, false);
        setFields(fields);

        if (addToHistory) {
          mementoHook.add({ ...data });

          const messageField = Object.keys(fields)[0];
          const formattedMessageField = messageField
            ? messageField
                .replace(/([A-Z])/g, ' $1')
                .trim()
                .toLocaleLowerCase()
            : '';

          const formattedMessageFieldCapitalized =
            formattedMessageField.charAt(0).toUpperCase() +
            formattedMessageField.slice(1);

          showMultiAlert({
            actionLabel: 'Undo',
            description: `${formattedMessageFieldCapitalized} updated`,
            variant: 'success',
          }).then(() => {
            const undoEvent = new KeyboardEvent('keydown', {
              bubbles: true,
              cancelable: true,
              ctrlKey: true,
              key: 'z',
            });
            document.dispatchEvent(undoEvent);
          });
        }
      })
      .catch((error) => {
        formatAndShowError(error);
        setFields(createFields(data, false, formatError(error)));
      });
  };

  const changeAddress = async (
    address: AmplifyAddress,
    addToHistory = true
  ) => {
    setFields({
      address: { error: null, loading: true },
    });

    await runMutation({
      addressCity: address.components?.addressCity,
      addressCountry: address.components?.addressCountry,
      addressLineOne: address.components?.addressLineOne,
      addressLineTwo: address.components?.addressLineTwo,
      addressPostcode: address.components?.addressPostcode,
      addressState: address.components?.addressState,
    })
      .then(() => {
        setFields({
          address: { error: null, loading: false },
        });
        setAddress(address);

        if (addToHistory) {
          mementoHook.add({ ...address.components });
          showMultiAlert({
            actionLabel: 'Undo',
            description: 'Address updated',
            variant: 'success',
          }).then(() => {
            const undoEvent = new KeyboardEvent('keydown', {
              bubbles: true,
              cancelable: true,
              ctrlKey: true,
              key: 'z',
            });
            document.dispatchEvent(undoEvent);
          });
        }
      })
      .catch((error) => {
        formatAndShowError(error);
        setFields({
          address: { error: formatError(error), loading: false },
        });
      });
  };

  let emails: {
    label: string;
    type?: 'registry' | 'hub';
  }[] = [];
  let phoneNumbers: { id: string; label: string }[] = [];
  let addresses: { id: string; label: string; value: AmplifyAddress }[] = [];

  if (contact.shareholdings) {
    const shareholdings = contact.shareholdings;

    emails = shareholdings
      .filter(
        (shareholding) =>
          shareholding.email !== null && shareholding.email !== undefined
      )
      .map((shareholding) => ({
        id: shareholding.id,
        label: shareholding.email as string,
        type: 'registry',
      }));

    phoneNumbers = shareholdings
      .filter(
        (shareholding) =>
          shareholding.phoneNumber !== null &&
          shareholding.phoneNumber !== undefined
      )
      .map((shareholding) => ({
        id: shareholding.id,
        label: shareholding.phoneNumber as string,
        type: 'registry',
      }));

    addresses = shareholdings
      .filter(
        (shareholding) =>
          shareholding.addressCity ||
          shareholding.addressCountry ||
          shareholding.addressPostcode ||
          shareholding.addressLineOne ||
          shareholding.addressLineTwo ||
          shareholding.addressState
      )
      .map((shareholding) => {
        const address = contactAddress(shareholding);
        return {
          id: shareholding.id,
          label: address.formattedAddress,
          type: 'registry',
          value: address,
        };
      });
  }

  if (contact.investor) {
    const { investor } = contact;
    emails.push({
      label: investor.email,
      type: 'hub',
    });
  }

  const mementoHook = useContactMemento(contact, changeData);

  return (
    <Grid cols={{ lg: 2, md: 2, sm: 1 }}>
      {/* LEFT SIDE */}
      {/* PERSONAL INFORMATION */}
      <Column spaceY={4}>
        <Card>
          <Card.Header>Personal information</Card.Header>
          <Card.Body>
            <Item
              icon={IdentificationIcon}
              label="First Name"
              value={
                <EditableText
                  confirmationOnClear={true}
                  fieldLabel="first name"
                  loading={fields['firstName']?.loading || false}
                  placeholder="＋ First Name"
                  value={contact?.firstName || ''}
                  onChange={(value) => changeData({ firstName: value.trim() })}
                />
              }
            />
            <Item
              icon={IdentificationIcon}
              label="Last Name"
              value={
                <EditableText
                  confirmationOnClear={true}
                  fieldLabel="last name"
                  loading={fields['lastName']?.loading || false}
                  placeholder="＋ Last Name"
                  value={contact?.lastName || ''}
                  onChange={(value) => changeData({ lastName: value.trim() })}
                />
              }
            />
            <Item
              icon={QuestionMarkCircleIcon}
              label="Contact source"
              value={renderContactSource}
            />
            <Item
              icon={ChatBubbleOvalLeftEllipsisIcon}
              label="Latest activity"
              value={
                <div className="pl-3">{renderedContactLatestActivity}</div>
              }
            />
          </Card.Body>
        </Card>

        {/* HUB INFORMATION */}

        {contact?.investor && (
          <Card>
            <Card.Header>Hub information</Card.Header>
            <Card.Body>
              <Item
                icon={UserCircleIcon}
                label="Hub member"
                value={
                  <div className="pl-3">
                    <Typography variant="text-button-sm">
                      {contact.investor.username}
                    </Typography>
                  </div>
                }
              />
              <Item
                icon={CalendarIcon}
                label="Joined hub"
                value={
                  <div className="pl-3">
                    <Typography variant="text-button-sm">
                      {dayjs(contact.investor.insertedAt).format(
                        'D MMMM YY [at] h:mm A'
                      )}
                    </Typography>
                  </div>
                }
              />
            </Card.Body>
          </Card>
        )}

        {/* GROUPS */}

        <Card>
          <Card.Header>Groups</Card.Header>
          <Card.Body>
            <Item
              icon={TagIcon}
              label="Tags"
              value={<EditableTags contact={contact} />}
            />
          </Card.Body>
        </Card>

        {/* SUBSCRIPTION PREFERENCES */}

        {contact?.email && (
          <Card>
            <Card.Header>Subscription preferences</Card.Header>
            <Card.Body>
              <Item
                icon={BellIcon}
                label="Email subscriptions"
                value={<Subscriptions contact={contact} />}
              />
            </Card.Body>
          </Card>
        )}

        {/* LEAD STATUS */}

        <Card>
          <Card.Header>Lead status</Card.Header>
          <Card.Body>
            <Item
              icon={FunnelIcon}
              label="Lead status"
              value={<LeadStatus contact={contact} />}
            />
          </Card.Body>
        </Card>
      </Column>

      {/* RIGHT SIDE */}

      {/* CONTACT DETAILS */}
      <Column spaceY={4}>
        <Card>
          <Card.Header>Contact details</Card.Header>
          <Card.Body>
            <Item
              icon={IdentificationIcon}
              label="Emails"
              tooltipDescription={`Email communications from ${currentCompanyProfileUser.profile.name} will only be sent to the primary email address for this contact.`}
              value={
                <>
                  <EditableText
                    canCopy={true}
                    confirmationOnClear={true}
                    error={fields['email']?.error}
                    fieldLabel="email"
                    hoverActions={[
                      {
                        description: `Email communications from ${currentCompanyProfileUser.profile.name} will only be sent to the primary email address for this contact.`,
                        icon: STAR_ICON.icon,
                      },
                    ]}
                    loading={fields['email']?.loading || false}
                    placeholder="＋ Email"
                    staticActions={
                      contact?.email
                        ? contact?.suppression
                          ? [STAR_ICON, NO_SYMBOL_ICON]
                          : [STAR_ICON]
                        : []
                    }
                    value={contact?.email || ''}
                    onChange={(value) => changeData({ email: value })}
                  />

                  <EditableFieldList
                    items={groupBy(
                      emails,
                      currentCompanyProfileUser?.profile?.registry || ''
                    )}
                    onSetPrimary={(item) => changeData({ email: item.label })}
                  />
                </>
              }
            />

            <Item
              icon={PhoneIcon}
              label="Phone"
              value={
                <>
                  <EditableText
                    canCopy={true}
                    confirmationOnClear={true}
                    error={fields['phoneNumber']?.error}
                    fieldLabel="phone"
                    hoverActions={[
                      {
                        description: 'Primary number for this contact.',
                        icon: STAR_ICON.icon,
                      },
                    ]}
                    loading={fields['phoneNumber']?.loading || false}
                    placeholder="＋ Phone"
                    staticActions={contact?.phoneNumber ? [STAR_ICON] : []}
                    value={contact?.phoneNumber || ''}
                    onChange={(value) => changeData({ phoneNumber: value })}
                  />
                  <EditableFieldList
                    items={groupBy(
                      phoneNumbers,
                      currentCompanyProfileUser?.profile?.registry || ''
                    )}
                    onSetPrimary={(item) =>
                      changeData({ phoneNumber: item.label })
                    }
                  />
                </>
              }
            />

            <Item
              icon={HomeIcon}
              label="Address"
              value={
                <>
                  <EditableAddressField
                    address={address}
                    error={fields['address']?.error}
                    hoverActions={[
                      {
                        description: 'Primary address for this contact.',
                        icon: STAR_ICON.icon,
                      },
                    ]}
                    loading={fields['address']?.loading || false}
                    placeholder="＋ Address"
                    setAddress={changeAddress}
                    staticActions={address.formattedAddress ? [STAR_ICON] : []}
                  />
                  <EditableFieldList
                    items={groupBy(
                      addresses,
                      currentCompanyProfileUser?.profile?.registry || ''
                    )}
                    onSetPrimary={(item) => {
                      if (item.value) changeAddress(item.value);
                    }}
                  />
                </>
              }
            />
          </Card.Body>
        </Card>

        {/* HIGH NET WORTH STATUS */}

        <Card>
          <Card.Header>High net worth status</Card.Header>
          <Card.Body>
            <Item
              icon={CurrencyDollarIcon}
              label="HNW status"
              value={<QualifiedInvestor contact={contact} />}
            />
          </Card.Body>
        </Card>

        {/* DEMOGRAPHICS */}

        <Card>
          <Card.Header>Demographics</Card.Header>
          <Card.Body>
            <Item
              icon={HashtagIcon}
              label="Age Range"
              value={
                <EditableChipField
                  allowMultiple={false}
                  allowSearch={false}
                  items={AVAILABLE_ITEMS}
                  loading={fields['ageRange']?.loading || false}
                  placeholder="+ Age range"
                  selectedItems={contact?.ageRange ? [contact.ageRange] : []}
                  onChange={(value) => changeData({ ageRange: value.join() })}
                />
              }
            />
            <Item
              icon={BriefcaseIcon}
              label="Occupation"
              value={
                <EditableText
                  loading={fields['occupation']?.loading || false}
                  placeholder="＋ Occupation"
                  value={contact?.occupation || ''}
                  onChange={(value) => changeData({ occupation: value })}
                />
              }
            />

            <Item
              icon={BuildingOfficeIcon}
              label="Company"
              value={
                <EditableText
                  loading={fields['company']?.loading || false}
                  placeholder="＋ Company"
                  value={contact?.company || ''}
                  onChange={(value) => changeData({ company: value })}
                />
              }
            />
          </Card.Body>
        </Card>
      </Column>
    </Grid>
  );
};

export default PersonalInfo;
