import { useMemo, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Modal,
  CheckCircleIcon as DSCheckCircleIcon,
} from '@ds';
import {
  ChartPieIcon,
  PlusIcon,
  UserGroupIcon,
} from '@heroicons/react/outline';
import { CheckCircleIcon } from '@heroicons/react/solid';
import {
  ArrowTrendingUpIcon,
  GlobeAmericasIcon,
} from '@heroicons/react-v2/24/outline';
import Image from 'next/image';
import { useIntercom } from 'react-use-intercom';
import { useNotifyOfBeneficialOwnersReportInterestMutation } from '@/apollo/generated';
import { useAlert } from '@/contexts/alert-context';

const LandingPage: React.ComponentType = () => {
  const { formatAndShowError } = useAlert();
  const { startSurvey } = useIntercom();
  const [notifyOfBeneficialOwnersReportInterest] =
    useNotifyOfBeneficialOwnersReportInterestMutation();
  const [successModalOpen, setSuccessModalOpen] = useState(false);

  const hasRegisteredInterest = useMemo(() => {
    if (typeof window === 'undefined') {
      return false;
    }
    return (
      localStorage.getItem('hasRegisteredBeneficialOwnersReportInterest') ===
        'true' || successModalOpen
    );
  }, [successModalOpen]);

  const onClickInterested = async () => {
    try {
      await notifyOfBeneficialOwnersReportInterest();
      localStorage.setItem(
        'hasRegisteredBeneficialOwnersReportInterest',
        'true'
      );
      setSuccessModalOpen(true);
      startSurvey(44930776);
    } catch (e) {
      formatAndShowError('Could not register interest. Please try again.');
    }
  };

  return (
    <div className="flex flex-col items-center justify-center gap-6 rounded-lg border border-gray-200 bg-white pb-8">
      <div>
        <Image
          alt="Top 20 investors example image"
          height={423}
          src="/images/beneficial-owners-reports/landing-page.svg"
          style={{ objectFit: 'contain' }}
          width={1106}
        />
      </div>
      <div className="max-w-[640px] space-y-2 text-center">
        <Typography className="text-gray-700" variant="text-heading-lg">
          Discover who owns your company.
        </Typography>
      </div>
      <div className="max-w-[800px] space-y-4 text-center">
        {[
          {
            icon: UserGroupIcon,
            msg: 'Uncover and manage your unmasked accounts.',
          },
          {
            icon: ArrowTrendingUpIcon,
            msg: 'Monitor changes to your ownership over time.',
          },
          {
            icon: ChartPieIcon,
            msg: 'Understand where control is concentrated.',
          },
          {
            icon: GlobeAmericasIcon,
            msg: 'Visualise your investors by geographic location.',
          },
        ].map(({ icon: Icon, msg }, index) => (
          <div key={index} className="flex items-center justify-center gap-2">
            <Icon className="h-4 w-4 text-gray-500" />
            <Typography className="text-gray-500" variant="text-body-md">
              {msg}
            </Typography>
          </div>
        ))}
      </div>
      <div className="flex gap-2">
        <Button
          LeadingIcon={hasRegisteredInterest ? CheckCircleIcon : undefined}
          TrailingIcon={hasRegisteredInterest ? undefined : PlusIcon}
          disabled={hasRegisteredInterest}
          onClick={() => onClickInterested()}
        >
          {hasRegisteredInterest ? 'Registered' : `Register interest`}
        </Button>
      </div>
      <SuccessModal
        open={successModalOpen}
        onClose={() => setSuccessModalOpen(false)}
      />
    </div>
  );
};

export default LandingPage;

const SuccessModal: React.ComponentType<{
  onClose: () => void;
  open: boolean;
}> = ({ onClose, open }) => {
  return (
    <Modal className="!max-w-[340px]" open={open} onClose={onClose}>
      <div className="space-y-2">
        <DSCheckCircleIcon />
        <Typography variant="text-label-lg">
          Thanks for registering your interest
        </Typography>
        <Typography className="text-gray-500" variant="text-body-md">
          Your Customer Success Manager will be in touch.
        </Typography>
        <Button className="w-full" onClick={onClose}>
          Got it, thanks!
        </Button>
      </div>
    </Modal>
  );
};
