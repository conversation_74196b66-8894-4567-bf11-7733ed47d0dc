import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import analytics from '@analytics';
import { Badge, Button, Typography, Modal, Checkbox } from '@ds';
import { SearchIcon } from '@heroicons/react/outline';
import clsx from 'clsx';
import truncate from 'lodash/truncate';
import Link from 'next/link';
import { useRouter } from 'next/router';
import numeral from 'numeral';
import {
  ContactQuery,
  ShareholdingsAndBeneficialOwnerAccountsQuery,
  useLinkContactWithBeneficialOwnerAccountsMutation,
  useLinkContactWithShareholdingsMutation,
  useShareholdingsAndBeneficialOwnerAccountsQuery,
} from '@/apollo/generated';
import ContactWithAvatar from '@/components/contacts/profile/modals/contact-with-avatar';
import { ContactLinkSearchBar } from '@/components/contacts/profile/search/bar';
import TableLoading from '@/components/utils/tables/loading';
import { useAlert } from '@/contexts/alert-context';
import routes from '@/utils/routes';

type AccountEdge = NonNullable<
  NonNullable<
    ShareholdingsAndBeneficialOwnerAccountsQuery['shareholdingsAndBeneficialOwnerAccounts']
  >['edges']
>[number];

interface Props {
  contact: NonNullable<ContactQuery['contact']>;
  linkedBeneficialOwnerAccounts: NonNullable<
    ContactQuery['contact']
  >['beneficialOwnerAccounts'];
  linkedShareholdings: NonNullable<ContactQuery['contact']>['shareholdings'];
  open: boolean;
  toggleOpen: Dispatch<SetStateAction<boolean>>;
}

const LinkAccountsModal: React.ComponentType<Props> = ({
  contact,
  linkedBeneficialOwnerAccounts,
  linkedShareholdings,
  open,
  toggleOpen,
}) => {
  const {
    query: { marketListingKey },
  } = useRouter();
  const contactId = contact.id;

  const contactName = useMemo(() => {
    return contact.firstName && contact.lastName
      ? `${contact.firstName} ${contact.lastName}`
      : contact.email || contact.phoneNumber || '-';
  }, [contact]);

  const [searchPhrase, setSearchPhrase] = useState('');
  const [shareholdingIds, setShareholdingIds] = useState<string[]>([]);
  const [boAccountIds, setBoAccountIds] = useState<string[]>([]);
  const linkedShareholdingsCount = linkedShareholdings.length;
  const linkedBeneficialOwnerAccountsCount =
    linkedBeneficialOwnerAccounts.length;
  const { formatAndShowError, showAlert } = useAlert();
  const { data, loading: accountsLoading } =
    useShareholdingsAndBeneficialOwnerAccountsQuery({
      skip: !open,
      variables: {
        first: 100,
        options: {
          filters: [{ key: 'search', value: `%${searchPhrase}%` }],
          orders: [
            { key: 'contact_id', value: contactId },
            { key: 'share_count', value: 'desc' },
          ],
        },
      },
    });
  const [linkContactWithShareholdings, { loading: linkShareholdingsLoading }] =
    useLinkContactWithShareholdingsMutation({
      awaitRefetchQueries: true,
      refetchQueries: ['Contact', 'ContactActivities', 'Shareholdings'],
      variables: {
        id: contactId,
        shareholdingIds,
      },
    });

  const [linkContactWithBeneficialOwnerAccounts, { loading: linkBOLoading }] =
    useLinkContactWithBeneficialOwnerAccountsMutation({
      variables: {
        beneficialOwnerAccountIds: boAccountIds,
        id: contactId,
      },
    });

  useEffect(() => {
    setShareholdingIds([]);
    setBoAccountIds([]);
  }, [data, open]);

  const onClose = useCallback(() => {
    toggleOpen(false);
    setSearchPhrase('');
  }, [toggleOpen]);

  const onSelectAccount = useCallback(
    (id: string, type: string, existingContactIsThisContact: boolean) => {
      if (!existingContactIsThisContact) {
        if (type === 'shareholding') {
          setShareholdingIds((prevValue) => {
            if (prevValue.includes(id)) {
              return prevValue.filter((pv) => pv !== id);
            }
            return [...prevValue, id];
          });
        } else {
          setBoAccountIds((prevValue) => {
            if (prevValue.includes(id)) {
              return prevValue.filter((pv) => pv !== id);
            }
            return [...prevValue, id];
          });
        }
      }
    },
    []
  );

  const onLink = useCallback(async () => {
    const accountsCount = shareholdingIds.length + boAccountIds.length;
    linkContactWithBeneficialOwnerAccounts()
      .then(() => {
        linkContactWithShareholdings()
          .then(() => {
            analytics.track('irm_contact_linked_with_shareholding');
            toggleOpen(false);
            setSearchPhrase('');
            showAlert({
              description: `${accountsCount} account${
                accountsCount === 1 ? '' : 's'
              } linked to this contact`,
              variant: 'success',
            });
          })
          .catch(formatAndShowError);
      })
      .catch(formatAndShowError);
  }, [
    formatAndShowError,
    linkContactWithShareholdings,
    linkContactWithBeneficialOwnerAccounts,
    boAccountIds,
    showAlert,
    toggleOpen,
    shareholdingIds,
  ]);

  const renderHeader = useCallback(() => {
    return (
      <div className="flex items-center rounded-t-lg border-b border-gray-200 bg-gray-50">
        <Typography
          className="w-[178px] min-w-[178px] whitespace-nowrap p-3 text-gray-500 md:w-[320px] md:min-w-[320px]"
          variant="text-button-sm"
        >
          Account name
        </Typography>
        <Typography
          className="hidden w-[142px] min-w-[142px] whitespace-nowrap p-3 text-gray-500 md:block"
          variant="text-button-sm"
        >
          Type
        </Typography>
        <Typography
          className="hidden w-[142px] min-w-[142px] whitespace-nowrap p-3 text-gray-500 md:block"
          variant="text-button-sm"
        >
          Linked contact
        </Typography>
      </div>
    );
  }, []);

  const renderContent = useCallback(() => {
    if (accountsLoading) {
      return (
        <div className="overflow-hidden rounded-lg border">
          <div className="table min-w-full">
            <TableLoading columnCount={3} rowHeight={'72px'} rowsPerPage={5} />
          </div>
        </div>
      );
    }

    const thisContactFirst = (a: AccountEdge, b: AccountEdge) => {
      if (!a?.node) return 1;
      if (a.node.contact?.id === contactId) return -1;
      if (a.node.contact) {
        return 1;
      } else return -1;
    };

    if (
      data?.shareholdingsAndBeneficialOwnerAccounts?.edges &&
      data.shareholdingsAndBeneficialOwnerAccounts.edges.length
    ) {
      // Sort the accounts so that the ones linked to this contact appear first
      // Sort twice so the one with highest share count appears first
      const ordered = [...data.shareholdingsAndBeneficialOwnerAccounts.edges]
        .sort(thisContactFirst)
        .sort(thisContactFirst);
      return (
        <div className="rounded-lg border">
          <div className="min-w-full">
            {renderHeader()}
            <div className="h-[310px] overflow-y-auto rounded-b-lg">
              {ordered.map((edge, idx) => {
                if (!edge || !edge.node) return null;
                const { node: account } = edge;
                const {
                  accountName,
                  accountType,
                  contact,
                  id: accountId,
                  shareCount,
                } = account;
                const active =
                  accountType === 'shareholding'
                    ? shareholdingIds.includes(accountId)
                    : boAccountIds.includes(accountId);
                const existingContactIsThisContact =
                  !!contact && contact.id === contactId;
                return (
                  <div
                    key={`${accountId}`}
                    className={clsx(
                      existingContactIsThisContact
                        ? 'cursor-not-allowed'
                        : 'cursor-pointer',
                      !existingContactIsThisContact && !active
                        ? 'hover:bg-gray-50'
                        : '',
                      'flex min-h-[72px] items-center p-4 transition-all',
                      data.shareholdingsAndBeneficialOwnerAccounts?.edges &&
                        idx ===
                          data.shareholdingsAndBeneficialOwnerAccounts.edges
                            .length -
                            1
                        ? ''
                        : 'border-b border-gray-200'
                    )}
                    onClick={() =>
                      onSelectAccount(
                        accountId,
                        accountType,
                        existingContactIsThisContact
                      )
                    }
                  >
                    <div className="w-[178px] min-w-[178px] truncate md:w-[320px] md:max-w-[320px] md:pr-3">
                      <div className="flex items-center gap-3">
                        <Checkbox
                          checked={
                            active || existingContactIsThisContact
                              ? 'yes'
                              : 'no'
                          }
                          disabled={existingContactIsThisContact}
                        />
                        <div className="flex flex-col gap-0 truncate">
                          <Typography
                            className={clsx(
                              active
                                ? 'text-amplify-green-700'
                                : 'text-gray-900',
                              'truncate'
                            )}
                            variant="text-label-sm"
                          >
                            {truncate(accountName, {
                              length: 50,
                            })}
                          </Typography>
                          <Typography
                            className="truncate text-gray-500"
                            variant="text-body-sm"
                          >
                            {`${numeral(shareCount || 0).format(
                              '0,000'
                            )} shares`}
                          </Typography>
                        </div>
                      </div>
                    </div>

                    <div className="z-0 hidden w-[142px] max-w-[142px] md:block">
                      {accountType === 'shareholding' ? (
                        <Badge className="z-0" color="fuchsia" size="xs">
                          Register
                        </Badge>
                      ) : (
                        <Badge className="z-0" color="moss" size="xs">
                          Unmasked accounts
                        </Badge>
                      )}
                    </div>

                    <div className="mr-auto hidden w-auto md:block">
                      {existingContactIsThisContact ? (
                        <Badge color="green" size="xs">
                          Linked to this contact
                        </Badge>
                      ) : contact ? (
                        <Link
                          href={routes.investors.search.contacts.contact.href(
                            marketListingKey as string,
                            contact?.id as string
                          )}
                          target="_blank"
                        >
                          <ContactWithAvatar contact={contact} />
                        </Link>
                      ) : (
                        <Typography
                          className="text-gray-500"
                          variant="text-body-sm"
                        >
                          Not linked yet
                        </Typography>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center gap-4">
        <div className="bg-fresh-neon-100 border-fresh-neon-25 flex h-14 w-14 items-center justify-center rounded-full border-8">
          <SearchIcon className="text-fresh-neon-900 h-6 w-6" />
        </div>
        <Typography className="text-gray-900" variant="text-heading-sm">
          No shareholders found
        </Typography>
        <Typography className="text-gray-500" variant="text-body-sm">
          Your search did not match any results. Please try again.
        </Typography>
      </div>
    );
  }, [
    contactId,
    data?.shareholdingsAndBeneficialOwnerAccounts?.edges,
    onSelectAccount,
    renderHeader,
    shareholdingIds,
    accountsLoading,
    marketListingKey,
    boAccountIds,
  ]);

  return (
    <Modal
      className="sm:max-w-[800px]"
      open={open}
      onClose={() => toggleOpen(false)}
    >
      <div className="space-y-4">
        <div>
          <Typography variant="text-display-sm">Add accounts</Typography>
          <Typography className="text-gray-600" variant="text-body-sm">
            {`Select accounts that belong to ${contactName}.`}
          </Typography>
        </div>

        {accountsLoading ? (
          <div className="space-y-1">
            <div className="mb-1 h-5 w-1/5 animate-pulse bg-gray-100" />
            <div className="mb-1 h-5 w-1/5 animate-pulse bg-gray-100" />
          </div>
        ) : (
          <div>
            <Typography className="text-gray-600" variant="text-label-sm">{`${
              data?.shareholdingsAndBeneficialOwnerAccounts?.total ?? 0
            } account${
              data?.shareholdingsAndBeneficialOwnerAccounts?.total === 1
                ? ''
                : 's'
            }`}</Typography>
            <Typography className="text-gray-600" variant="text-body-sm">{`${
              linkedShareholdingsCount + linkedBeneficialOwnerAccountsCount
            } linked, ${
              shareholdingIds.length + boAccountIds.length
            } selected`}</Typography>
          </div>
        )}
        <div>
          <ContactLinkSearchBar
            placeholder="Search by registry account name"
            searchPhrase={searchPhrase}
            setSearchPhrase={setSearchPhrase}
          />
        </div>
        {renderContent()}
        <div className="flex w-full items-center gap-4 pt-2">
          <Button className="w-full" variant="secondary-gray" onClick={onClose}>
            Cancel
          </Button>
          <Button
            className="w-full"
            disabled={
              linkBOLoading ||
              linkShareholdingsLoading ||
              !(shareholdingIds.length || boAccountIds.length)
            }
            onClick={onLink}
          >
            Add to contact
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default LinkAccountsModal;
